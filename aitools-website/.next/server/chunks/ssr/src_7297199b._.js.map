{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/HeaderClient.tsx"], "sourcesContent": ["'use client';\n\nimport { Link as NextLink } from '@/i18n/routing';\nimport { useTranslations, useLocale } from 'next-intl';\nimport { type Locale } from '@/i18n/config';\nimport UserMenuClient from '../auth/UserMenuClient';\nimport LanguageSwitcherClient from './LanguageSwitcherClient';\nimport MobileMenuClient from './MobileMenuClient';\nimport SearchFormClient from './SearchFormClient';\n\nconst NavLink = ({ children, href }: { children: React.ReactNode; href: string }) => (\n  <NextLink\n    href={href}\n    className=\"px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors\"\n  >\n    {children}\n  </NextLink>\n);\n\nexport default function HeaderClient() {\n  const t = useTranslations('navigation');\n  const locale = useLocale() as Locale;\n\n  const links = [\n    { name: t('home'), href: '/' },\n    { name: t('tools'), href: '/tools' },\n    { name: t('categories'), href: '/categories' },\n    { name: t('submit'), href: '/submit' },\n  ];\n\n  return (\n    <header className=\"bg-white px-4 shadow-sm border-b border-gray-200\">\n      <div className=\"flex h-16 items-center justify-between\">\n        {/* Logo */}\n        <div className=\"flex items-center space-x-8\">\n          <NextLink href=\"/\" className=\"flex items-center space-x-2 hover:no-underline\">\n            <div className=\"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">AI</span>\n            </div>\n            <span className=\"text-xl font-bold text-gray-900\">\n              {locale === 'zh' ? 'AI工具导航' : 'AI Tools'}\n            </span>\n          </NextLink>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-4\">\n            {links.map((link) => (\n              <NavLink key={link.name} href={link.href}>\n                {link.name}\n              </NavLink>\n            ))}\n          </nav>\n        </div>\n\n        {/* Search Bar */}\n        <div className=\"flex-1 max-w-md mx-8 hidden md:block\">\n          <SearchFormClient locale={locale} />\n        </div>\n\n        {/* Right side */}\n        <div className=\"flex items-center space-x-2\">\n          {/* Language Switcher */}\n          <LanguageSwitcherClient currentLocale={locale} />\n\n          {/* User Menu */}\n          <UserMenuClient />\n\n          {/* Mobile Menu */}\n          <MobileMenuClient links={links} locale={locale} />\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AACA;AACA;AACA;AARA;;;;;;;;AAUA,MAAM,UAAU,CAAC,EAAE,QAAQ,EAAE,IAAI,EAA+C,iBAC9E,8OAAC,sHAAA,CAAA,OAAQ;QACP,MAAM;QACN,WAAU;kBAET;;;;;;AAIU,SAAS;IACtB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,QAAQ;QACZ;YAAE,MAAM,EAAE;YAAS,MAAM;QAAI;QAC7B;YAAE,MAAM,EAAE;YAAU,MAAM;QAAS;QACnC;YAAE,MAAM,EAAE;YAAe,MAAM;QAAc;QAC7C;YAAE,MAAM,EAAE;YAAW,MAAM;QAAU;KACtC;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sHAAA,CAAA,OAAQ;4BAAC,MAAK;4BAAI,WAAU;;8CAC3B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAK,WAAU;8CACb,WAAW,OAAO,WAAW;;;;;;;;;;;;sCAKlC,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;oCAAwB,MAAM,KAAK,IAAI;8CACrC,KAAK,IAAI;mCADE,KAAK,IAAI;;;;;;;;;;;;;;;;8BAQ7B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gJAAA,CAAA,UAAgB;wBAAC,QAAQ;;;;;;;;;;;8BAI5B,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,sJAAA,CAAA,UAAsB;4BAAC,eAAe;;;;;;sCAGvC,8OAAC,4IAAA,CAAA,UAAc;;;;;sCAGf,8OAAC,gJAAA,CAAA,UAAgB;4BAAC,OAAO;4BAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;AAKlD", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/FooterClient.tsx"], "sourcesContent": ["'use client';\n\nimport { Link as NextLink } from '@/i18n/routing';\nimport { useTranslations, useLocale } from 'next-intl';\nimport { type Locale } from '@/i18n/config';\n\nexport default function FooterClient() {\n  const t = useTranslations('layout');\n  const locale = useLocale() as Locale;\n\n  return (\n    <footer className=\"bg-white border-t border-gray-200 mt-16\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">AI</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">\n                {locale === 'zh' ? 'AI工具导航' : 'AI Tools'}\n              </span>\n            </div>\n            <p className=\"text-gray-600 mb-4\">\n              {t('footer.description')}\n            </p>\n          </div>\n          \n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n              {t('footer.quick_links')}\n            </h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <NextLink href=\"/tools\" className=\"text-gray-600 hover:text-blue-600\">\n                  {t('footer.tools_directory')}\n                </NextLink>\n              </li>\n              <li>\n                <NextLink href=\"/categories\" className=\"text-gray-600 hover:text-blue-600\">\n                  {t('footer.browse_categories')}\n                </NextLink>\n              </li>\n              <li>\n                <NextLink href=\"/submit\" className=\"text-gray-600 hover:text-blue-600\">\n                  {t('footer.submit_tool')}\n                </NextLink>\n              </li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n              {t('footer.support')}\n            </h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                  {t('footer.help_center')}\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                  {t('footer.contact_us')}\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                  {t('footer.privacy_policy')}\n                </a>\n              </li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"border-t border-gray-200 mt-8 pt-8\">\n          <p className=\"text-center text-gray-600\">\n            {t('footer.copyright')}\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAMe,SAAS;IACtB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IAEvB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDACb,WAAW,OAAO,WAAW;;;;;;;;;;;;8CAGlC,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAIP,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,sHAAA,CAAA,OAAQ;gDAAC,MAAK;gDAAS,WAAU;0DAC/B,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC,sHAAA,CAAA,OAAQ;gDAAC,MAAK;gDAAc,WAAU;0DACpC,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC,sHAAA,CAAA,OAAQ;gDAAC,MAAK;gDAAU,WAAU;0DAChC,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAMX,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACnB,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACnB,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACnB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCACV,EAAE;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 429, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // 优先发布服务价格\n  PRIORITY_LAUNCH: {\n    // 显示价格（元）\n    displayPrice: 19.9,\n    // Stripe价格（分为单位）\n    stripeAmount: 1990,\n    // 货币\n    currency: 'USD',\n    // Stripe货币代码（小写）\n    stripeCurrency: 'usd', // 注意：当前使用USD进行测试\n    // 产品名称\n    productName: 'AI工具优先发布服务',\n    // 产品描述\n    description: '让您的AI工具获得优先审核和推荐位置',\n    // 功能特性\n    features: [\n      '可选择任意发布日期',\n      '优先审核处理',\n      '首页推荐位置',\n      '专属客服支持'\n    ]\n  },\n  \n  // 免费发布配置\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: '免费发布服务',\n    description: '选择一个月后的任意发布日期',\n    features: [\n      '免费提交审核',\n      '发布日期：一个月后起',\n      '正常审核流程',\n      '标准展示位置'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: '免费',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: '免费增值',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: '付费',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: '所有价格' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number) => {\n  return price === 0 ? '免费' : `¥${price}`;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,WAAW;IACX,iBAAiB;QACf,UAAU;QACV,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,KAAK;QACL,UAAU;QACV,iBAAiB;QACjB,gBAAgB;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,SAAS;IACT,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAO;IAC3B;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC;IAC1B,OAAO,UAAU,IAAI,OAAO,CAAC,CAAC,EAAE,OAAO;AACzC;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { usePathname } from '@/i18n/routing';\nimport { useTranslations } from 'next-intl';\nimport {\n  useStripe,\n  useElements,\n  PaymentElement,\n  AddressElement\n} from '@stripe/react-stripe-js';\nimport { CreditCard, Loader2 } from 'lucide-react';\nimport { formatStripeAmount } from '@/constants/pricing';\nimport { Locale } from '@/i18n/config';\n\ninterface StripeCheckoutFormProps {\n  onSuccess: () => void;\n  amount: number;\n}\n\nexport default function StripeCheckoutForm({ onSuccess, amount }: StripeCheckoutFormProps) {\n  const stripe = useStripe();\n  const elements = useElements();\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [errorMessage, setErrorMessage] = useState<string>('');\n\n  const pathname = usePathname();\n  const t = useTranslations('payment');\n\n  // Extract current locale from pathname\n  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;\n\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n\n    if (!stripe || !elements) {\n      return;\n    }\n\n    setIsProcessing(true);\n    setErrorMessage('');\n\n    try {\n      // 确认支付\n      const { error } = await stripe.confirmPayment({\n        elements,\n        confirmParams: {\n          return_url: `${window.location.origin}/submit/success`,\n        },\n        redirect: 'if_required'\n      });\n\n      if (error) {\n        // 支付失败\n        if (error.type === 'card_error' || error.type === 'validation_error') {\n          setErrorMessage(error.message || t('payment_failed'));\n        } else {\n          setErrorMessage(t('payment_error'));\n        }\n      } else {\n        // 支付成功\n        onSuccess();\n      }\n    } catch (err) {\n      setErrorMessage(t('payment_processing_failed'));\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      {/* 支付方式选择 */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n          {t('payment_method')}\n        </label>\n        <PaymentElement \n          options={{\n            layout: 'tabs',\n            defaultValues: {\n              billingDetails: {\n                address: {\n                  country: 'CN'\n                }\n              }\n            }\n          }}\n        />\n      </div>\n\n      {/* 账单地址 */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n          {t('billing_address')}\n        </label>\n        <AddressElement \n          options={{\n            mode: 'billing',\n            defaultValues: {\n              address: {\n                country: 'CN'\n              }\n            }\n          }}\n        />\n      </div>\n\n      {/* 错误信息 */}\n      {errorMessage && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <p className=\"text-red-600 text-sm\">{errorMessage}</p>\n        </div>\n      )}\n\n      {/* 支付按钮 */}\n      <button\n        type=\"submit\"\n        disabled={!stripe || !elements || isProcessing}\n        className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors\"\n      >\n        {isProcessing ? (\n          <>\n            <Loader2 className=\"h-5 w-5 mr-2 animate-spin\" />\n            {t('processing_payment')}\n          </>\n        ) : (\n          <>\n            <CreditCard className=\"h-5 w-5 mr-2\" />\n            {t('pay_now', { amount: formatStripeAmount(amount) })}\n          </>\n        )}\n      </button>\n\n      {/* 安全提示 */}\n      <div className=\"text-center\">\n        <p className=\"text-gray-500 text-xs\">\n          {t('security_notice')}\n        </p>\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AAAA;AACA;AAZA;;;;;;;;AAoBe,SAAS,mBAAmB,EAAE,SAAS,EAAE,MAAM,EAA2B;IACvF,MAAM,SAAS,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,uCAAuC;IACvC,MAAM,gBAAiB,UAAU,WAAW,SAAS,OAAO;IAE5D,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QAEpB,IAAI,CAAC,UAAU,CAAC,UAAU;YACxB;QACF;QAEA,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,OAAO;YACP,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,cAAc,CAAC;gBAC5C;gBACA,eAAe;oBACb,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC;gBACxD;gBACA,UAAU;YACZ;YAEA,IAAI,OAAO;gBACT,OAAO;gBACP,IAAI,MAAM,IAAI,KAAK,gBAAgB,MAAM,IAAI,KAAK,oBAAoB;oBACpE,gBAAgB,MAAM,OAAO,IAAI,EAAE;gBACrC,OAAO;oBACL,gBAAgB,EAAE;gBACpB;YACF,OAAO;gBACL,OAAO;gBACP;YACF;QACF,EAAE,OAAO,KAAK;YACZ,gBAAgB,EAAE;QACpB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BAEtC,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCACd,EAAE;;;;;;kCAEL,8OAAC,oLAAA,CAAA,iBAAc;wBACb,SAAS;4BACP,QAAQ;4BACR,eAAe;gCACb,gBAAgB;oCACd,SAAS;wCACP,SAAS;oCACX;gCACF;4BACF;wBACF;;;;;;;;;;;;0BAKJ,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCACd,EAAE;;;;;;kCAEL,8OAAC,oLAAA,CAAA,iBAAc;wBACb,SAAS;4BACP,MAAM;4BACN,eAAe;gCACb,SAAS;oCACP,SAAS;gCACX;4BACF;wBACF;;;;;;;;;;;;YAKH,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;0BAKzC,8OAAC;gBACC,MAAK;gBACL,UAAU,CAAC,UAAU,CAAC,YAAY;gBAClC,WAAU;0BAET,6BACC;;sCACE,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAClB,EAAE;;iDAGL;;sCACE,8OAAC,kNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;wBACrB,EAAE,WAAW;4BAAE,QAAQ,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE;wBAAQ;;;;;;;;0BAMzD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BACV,EAAE;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/payment/checkout/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from '@/i18n/routing';\nimport { useSearchParams } from 'next/navigation';\nimport { useSession } from 'next-auth/react';\nimport { loadStripe } from '@stripe/stripe-js';\nimport { Elements } from '@stripe/react-stripe-js';\nimport HeaderClient from '@/components/layout/HeaderClient';\nimport FooterClient from '@/components/layout/FooterClient';\nimport StripeCheckoutForm from '@/components/StripeCheckoutForm';\nimport { formatStripeAmount } from '@/constants/pricing';\nimport { CreditCard, Shield, CheckCircle, AlertCircle } from 'lucide-react';\n\n// 初始化Stripe\nconst stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);\n\nexport default function CheckoutPage() {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const { data: session, status } = useSession();\n  const [order, setOrder] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [clientSecret, setClientSecret] = useState<string>('');\n\n  const orderId = searchParams.get('orderId');\n\n  useEffect(() => {\n    if (status === 'unauthenticated') {\n      router.push('/');\n      return;\n    }\n\n    if (status === 'authenticated' && orderId) {\n      fetchOrderInfo();\n    }\n  }, [status, orderId]);\n\n  const fetchOrderInfo = async () => {\n    try {\n      const response = await fetch(`/api/orders/${orderId}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setOrder(data.data);\n\n        // 检查订单状态\n        if (data.data.status === 'completed') {\n          router.push(`/submit/success?toolId=${data.data.toolId}`);\n          return;\n        }\n\n        if (data.data.status !== 'pending') {\n          setError('订单状态异常');\n          return;\n        }\n\n        // 创建支付意图\n        await createPaymentIntent();\n      } else {\n        setError('订单不存在');\n      }\n    } catch (err) {\n      setError('获取订单信息失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const createPaymentIntent = async () => {\n    try {\n      const response = await fetch('/api/stripe/create-payment-intent', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ orderId }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setClientSecret(data.data.clientSecret);\n      } else {\n        setError(data.message || '创建支付失败');\n      }\n    } catch (err) {\n      setError('创建支付失败');\n    }\n  };\n\n  const handlePaymentSuccess = () => {\n    router.push(`/submit/success?toolId=${order.toolId}&paid=true`);\n  };\n\n  if (loading) {\n    return (\n      <>\n        <HeaderClient />\n        <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">加载订单信息...</p>\n          </div>\n        </div>\n        <FooterClient />\n      </>\n    );\n  }\n\n  if (error) {\n    return (\n      <>\n        <HeaderClient />\n        <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center\">\n            <AlertCircle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">支付出错</h1>\n            <p className=\"text-gray-600 mb-4\">{error}</p>\n            <button\n              onClick={() => router.push('/submit')}\n              className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700\"\n            >\n              返回提交页面\n            </button>\n          </div>\n        </div>\n        <FooterClient />\n      </>\n    );\n  }\n\n  if (!order) {\n    return null;\n  }\n\n  return (\n    <>\n      <HeaderClient />\n      <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <CreditCard className=\"h-12 w-12 text-blue-600 mx-auto mb-4\" />\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            完成支付\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            为您的工具选择优先发布服务\n          </p>\n        </div>\n\n        {/* Order Summary */}\n        <div className=\"bg-white border border-gray-200 rounded-lg p-6 mb-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">订单详情</h2>\n          \n          <div className=\"space-y-3\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">服务类型</span>\n              <span className=\"font-medium\">工具优先发布</span>\n            </div>\n            \n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">工具名称</span>\n              <span className=\"font-medium\">{order.tool?.name || '加载中...'}</span>\n            </div>\n            \n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">发布日期</span>\n              <span className=\"font-medium\">\n                {new Date(order.selectedLaunchDate).toLocaleDateString('zh-CN')}\n              </span>\n            </div>\n            \n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">订单号</span>\n              <span className=\"font-medium text-sm\">{order._id}</span>\n            </div>\n            \n            <hr className=\"my-4\" />\n            \n            <div className=\"flex justify-between text-lg font-semibold\">\n              <span>总计</span>\n              <span className=\"text-blue-600\">{formatStripeAmount(order.amount)}</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Service Features */}\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6\">\n          <h3 className=\"text-lg font-semibold text-blue-900 mb-3\">\n            优先发布服务包含：\n          </h3>\n          <ul className=\"space-y-2\">\n            <li className=\"flex items-center text-blue-800\">\n              <CheckCircle className=\"h-4 w-4 mr-2 flex-shrink-0\" />\n              可选择任意发布日期\n            </li>\n            <li className=\"flex items-center text-blue-800\">\n              <CheckCircle className=\"h-4 w-4 mr-2 flex-shrink-0\" />\n              优先审核处理（1个工作日内）\n            </li>\n            <li className=\"flex items-center text-blue-800\">\n              <CheckCircle className=\"h-4 w-4 mr-2 flex-shrink-0\" />\n              首页推荐位置展示\n            </li>\n            <li className=\"flex items-center text-blue-800\">\n              <CheckCircle className=\"h-4 w-4 mr-2 flex-shrink-0\" />\n              专属客服支持\n            </li>\n          </ul>\n        </div>\n\n        {/* Security Notice */}\n        <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6\">\n          <div className=\"flex items-center text-gray-700\">\n            <Shield className=\"h-5 w-5 mr-2 text-green-500\" />\n            <span className=\"text-sm\">\n              您的支付信息受到银行级别的安全保护\n            </span>\n          </div>\n        </div>\n\n        {/* Stripe Payment Form */}\n        {clientSecret && (\n          <Elements\n            stripe={stripePromise}\n            options={{\n              clientSecret,\n              appearance: {\n                theme: 'stripe',\n                variables: {\n                  colorPrimary: '#2563eb',\n                }\n              }\n            }}\n          >\n            <StripeCheckoutForm\n              onSuccess={handlePaymentSuccess}\n              amount={order.amount}\n            />\n          </Elements>\n        )}\n\n        {error && (\n          <div className=\"text-center\">\n            <p className=\"text-red-600 text-sm mt-4\">{error}</p>\n          </div>\n        )}\n\n        <p className=\"text-gray-500 text-sm mt-4 text-center\">\n          点击支付即表示您同意我们的服务条款和隐私政策\n        </p>\n      </div>\n      <FooterClient />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAZA;;;;;;;;;;;;;AAcA,YAAY;AACZ,MAAM,gBAAgB,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD;AAEhB,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACxC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,MAAM,UAAU,aAAa,GAAG,CAAC;IAEjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,mBAAmB;YAChC,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,WAAW,mBAAmB,SAAS;YACzC;QACF;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,SAAS;YACrD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS,KAAK,IAAI;gBAElB,SAAS;gBACT,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,aAAa;oBACpC,OAAO,IAAI,CAAC,CAAC,uBAAuB,EAAE,KAAK,IAAI,CAAC,MAAM,EAAE;oBACxD;gBACF;gBAEA,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,WAAW;oBAClC,SAAS;oBACT;gBACF;gBAEA,SAAS;gBACT,MAAM;YACR,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qCAAqC;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAQ;YACjC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,gBAAgB,KAAK,IAAI,CAAC,YAAY;YACxC,OAAO;gBACL,SAAS,KAAK,OAAO,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX;IACF;IAEA,MAAM,uBAAuB;QAC3B,OAAO,IAAI,CAAC,CAAC,uBAAuB,EAAE,MAAM,MAAM,CAAC,UAAU,CAAC;IAChE;IAEA,IAAI,SAAS;QACX,qBACE;;8BACE,8OAAC,4IAAA,CAAA,UAAY;;;;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;8BAGtC,8OAAC,4IAAA,CAAA,UAAY;;;;;;;IAGnB;IAEA,IAAI,OAAO;QACT,qBACE;;8BACE,8OAAC,4IAAA,CAAA,UAAY;;;;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;0CACnC,8OAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAKL,8OAAC,4IAAA,CAAA,UAAY;;;;;;;IAGnB;IAEA,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,qBACE;;0BACE,8OAAC,4IAAA,CAAA,UAAY;;;;;0BACb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAMvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAEzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAGhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DAAe,MAAM,IAAI,EAAE,QAAQ;;;;;;;;;;;;kDAGrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DACb,IAAI,KAAK,MAAM,kBAAkB,EAAE,kBAAkB,CAAC;;;;;;;;;;;;kDAI3D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DAAuB,MAAM,GAAG;;;;;;;;;;;;kDAGlD,8OAAC;wCAAG,WAAU;;;;;;kDAEd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;gDAAK,WAAU;0DAAiB,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;kCAMtE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAA+B;;;;;;;kDAGxD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAA+B;;;;;;;kDAGxD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAA+B;;;;;;;kDAGxD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAA+B;;;;;;;;;;;;;;;;;;;kCAO5D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;oBAO7B,8BACC,8OAAC,oLAAA,CAAA,WAAQ;wBACP,QAAQ;wBACR,SAAS;4BACP;4BACA,YAAY;gCACV,OAAO;gCACP,WAAW;oCACT,cAAc;gCAChB;4BACF;wBACF;kCAEA,cAAA,8OAAC,wIAAA,CAAA,UAAkB;4BACjB,WAAW;4BACX,QAAQ,MAAM,MAAM;;;;;;;;;;;oBAKzB,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;kCAI9C,8OAAC;wBAAE,WAAU;kCAAyC;;;;;;;;;;;;0BAIxD,8OAAC,4IAAA,CAAA,UAAY;;;;;;;AAGnB", "debugId": null}}]}