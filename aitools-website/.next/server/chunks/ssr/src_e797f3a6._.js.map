{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // 优先发布服务价格\n  PRIORITY_LAUNCH: {\n    // 显示价格（元）\n    displayPrice: 19.9,\n    // Stripe价格（分为单位）\n    stripeAmount: 1990,\n    // 货币\n    currency: 'USD',\n    // Stripe货币代码（小写）\n    stripeCurrency: 'usd', // 注意：当前使用USD进行测试\n    // 产品名称\n    productName: 'AI工具优先发布服务',\n    // 产品描述\n    description: '让您的AI工具获得优先审核和推荐位置',\n    // 功能特性\n    features: [\n      '可选择任意发布日期',\n      '优先审核处理',\n      '首页推荐位置',\n      '专属客服支持'\n    ]\n  },\n  \n  // 免费发布配置\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: '免费发布服务',\n    description: '选择一个月后的任意发布日期',\n    features: [\n      '免费提交审核',\n      '发布日期：一个月后起',\n      '正常审核流程',\n      '标准展示位置'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: '免费',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: '免费增值',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: '付费',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: '所有价格' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number) => {\n  return price === 0 ? '免费' : `¥${price}`;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,WAAW;IACX,iBAAiB;QACf,UAAU;QACV,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,KAAK;QACL,UAAU;QACV,iBAAiB;QACjB,gBAAgB;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,SAAS;IACT,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAO;IAC3B;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC;IAC1B,OAAO,UAAU,IAAI,OAAO,CAAC,CAAC,EAAE,OAAO;AACzC;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { usePathname } from '@/i18n/routing';\nimport { useTranslations } from 'next-intl';\nimport {\n  useStripe,\n  useElements,\n  PaymentElement,\n  AddressElement\n} from '@stripe/react-stripe-js';\nimport { CreditCard, Loader2 } from 'lucide-react';\nimport { formatStripeAmount } from '@/constants/pricing';\nimport { Locale } from '@/i18n/config';\n\ninterface StripeCheckoutFormProps {\n  onSuccess: () => void;\n  amount: number;\n}\n\nexport default function StripeCheckoutForm({ onSuccess, amount }: StripeCheckoutFormProps) {\n  const stripe = useStripe();\n  const elements = useElements();\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [errorMessage, setErrorMessage] = useState<string>('');\n\n  const pathname = usePathname();\n  const t = useTranslations('payment');\n\n  // Extract current locale from pathname\n  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;\n\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n\n    if (!stripe || !elements) {\n      return;\n    }\n\n    setIsProcessing(true);\n    setErrorMessage('');\n\n    try {\n      // 确认支付\n      const { error } = await stripe.confirmPayment({\n        elements,\n        confirmParams: {\n          return_url: `${window.location.origin}/submit/success`,\n        },\n        redirect: 'if_required'\n      });\n\n      if (error) {\n        // 支付失败\n        if (error.type === 'card_error' || error.type === 'validation_error') {\n          setErrorMessage(error.message || t('payment_failed'));\n        } else {\n          setErrorMessage(t('payment_error'));\n        }\n      } else {\n        // 支付成功\n        onSuccess();\n      }\n    } catch (err) {\n      setErrorMessage(t('payment_processing_failed'));\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      {/* 支付方式选择 */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n          {t('payment_method')}\n        </label>\n        <PaymentElement \n          options={{\n            layout: 'tabs',\n            defaultValues: {\n              billingDetails: {\n                address: {\n                  country: 'CN'\n                }\n              }\n            }\n          }}\n        />\n      </div>\n\n      {/* 账单地址 */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n          {t('billing_address')}\n        </label>\n        <AddressElement \n          options={{\n            mode: 'billing',\n            defaultValues: {\n              address: {\n                country: 'CN'\n              }\n            }\n          }}\n        />\n      </div>\n\n      {/* 错误信息 */}\n      {errorMessage && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <p className=\"text-red-600 text-sm\">{errorMessage}</p>\n        </div>\n      )}\n\n      {/* 支付按钮 */}\n      <button\n        type=\"submit\"\n        disabled={!stripe || !elements || isProcessing}\n        className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors\"\n      >\n        {isProcessing ? (\n          <>\n            <Loader2 className=\"h-5 w-5 mr-2 animate-spin\" />\n            {t('processing_payment')}\n          </>\n        ) : (\n          <>\n            <CreditCard className=\"h-5 w-5 mr-2\" />\n            {t('pay_now', { amount: formatStripeAmount(amount) })}\n          </>\n        )}\n      </button>\n\n      {/* 安全提示 */}\n      <div className=\"text-center\">\n        <p className=\"text-gray-500 text-xs\">\n          {t('security_notice')}\n        </p>\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AAAA;AACA;AAZA;;;;;;;;AAoBe,SAAS,mBAAmB,EAAE,SAAS,EAAE,MAAM,EAA2B;IACvF,MAAM,SAAS,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,uCAAuC;IACvC,MAAM,gBAAiB,UAAU,WAAW,SAAS,OAAO;IAE5D,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QAEpB,IAAI,CAAC,UAAU,CAAC,UAAU;YACxB;QACF;QAEA,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,OAAO;YACP,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,cAAc,CAAC;gBAC5C;gBACA,eAAe;oBACb,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC;gBACxD;gBACA,UAAU;YACZ;YAEA,IAAI,OAAO;gBACT,OAAO;gBACP,IAAI,MAAM,IAAI,KAAK,gBAAgB,MAAM,IAAI,KAAK,oBAAoB;oBACpE,gBAAgB,MAAM,OAAO,IAAI,EAAE;gBACrC,OAAO;oBACL,gBAAgB,EAAE;gBACpB;YACF,OAAO;gBACL,OAAO;gBACP;YACF;QACF,EAAE,OAAO,KAAK;YACZ,gBAAgB,EAAE;QACpB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BAEtC,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCACd,EAAE;;;;;;kCAEL,8OAAC,oLAAA,CAAA,iBAAc;wBACb,SAAS;4BACP,QAAQ;4BACR,eAAe;gCACb,gBAAgB;oCACd,SAAS;wCACP,SAAS;oCACX;gCACF;4BACF;wBACF;;;;;;;;;;;;0BAKJ,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCACd,EAAE;;;;;;kCAEL,8OAAC,oLAAA,CAAA,iBAAc;wBACb,SAAS;4BACP,MAAM;4BACN,eAAe;gCACb,SAAS;oCACP,SAAS;gCACX;4BACF;wBACF;;;;;;;;;;;;YAKH,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;0BAKzC,8OAAC;gBACC,MAAK;gBACL,UAAU,CAAC,UAAU,CAAC,YAAY;gBAClC,WAAU;0BAET,6BACC;;sCACE,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAClB,EAAE;;iDAGL;;sCACE,8OAAC,kNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;wBACrB,EAAE,WAAW;4BAAE,QAAQ,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE;wBAAQ;;;;;;;;0BAMzD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BACV,EAAE;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/payment/checkout/CheckoutClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from '@/i18n/routing';\nimport { loadStripe } from '@stripe/stripe-js';\nimport { Elements } from '@stripe/react-stripe-js';\nimport StripeCheckoutForm from '@/components/StripeCheckoutForm';\nimport { formatStripeAmount } from '@/constants/pricing';\nimport { CreditCard, Shield, CheckCircle, AlertCircle } from 'lucide-react';\n\n// 初始化Stripe\nconst stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);\n\ninterface CheckoutClientProps {\n  order: any;\n  orderId: string;\n}\n\nexport default function CheckoutClient({ order, orderId }: CheckoutClientProps) {\n  const router = useRouter();\n  const [clientSecret, setClientSecret] = useState<string>('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    createPaymentIntent();\n  }, []);\n\n  const createPaymentIntent = async () => {\n    try {\n      const response = await fetch('/api/stripe/create-payment-intent', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ orderId }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setClientSecret(data.data.clientSecret);\n      } else {\n        setError(data.message || '创建支付失败');\n      }\n    } catch (err) {\n      setError('创建支付失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePaymentSuccess = () => {\n    router.push(`/submit/success?toolId=${order.toolId}&paid=true`);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">创建支付会话...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"text-center\">\n          <AlertCircle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">支付出错</h1>\n          <p className=\"text-gray-600 mb-4\">{error}</p>\n          <button\n            onClick={() => router.push('/submit')}\n            className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            返回提交页面\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"text-center mb-8\">\n        <CreditCard className=\"h-12 w-12 text-blue-600 mx-auto mb-4\" />\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          完成支付\n        </h1>\n        <p className=\"text-lg text-gray-600\">\n          为您的工具选择优先发布服务\n        </p>\n      </div>\n\n      {/* Order Summary */}\n      <div className=\"bg-white border border-gray-200 rounded-lg p-6 mb-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">订单详情</h2>\n        \n        <div className=\"space-y-3\">\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">服务类型</span>\n            <span className=\"font-medium\">工具优先发布</span>\n          </div>\n          \n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">工具名称</span>\n            <span className=\"font-medium\">{order.tool?.name || '加载中...'}</span>\n          </div>\n          \n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">发布日期</span>\n            <span className=\"font-medium\">\n              {new Date(order.selectedLaunchDate).toLocaleDateString('zh-CN')}\n            </span>\n          </div>\n          \n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">订单号</span>\n            <span className=\"font-medium text-sm\">{order._id}</span>\n          </div>\n          \n          <hr className=\"my-4\" />\n          \n          <div className=\"flex justify-between text-lg font-semibold\">\n            <span>总计</span>\n            <span className=\"text-blue-600\">{formatStripeAmount(order.amount)}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Service Features */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6\">\n        <h3 className=\"text-lg font-semibold text-blue-900 mb-3\">\n          优先发布服务包含：\n        </h3>\n        <ul className=\"space-y-2\">\n          <li className=\"flex items-center text-blue-800\">\n            <CheckCircle className=\"h-4 w-4 mr-2 flex-shrink-0\" />\n            可选择任意发布日期\n          </li>\n          <li className=\"flex items-center text-blue-800\">\n            <CheckCircle className=\"h-4 w-4 mr-2 flex-shrink-0\" />\n            优先审核处理（1个工作日内）\n          </li>\n          <li className=\"flex items-center text-blue-800\">\n            <CheckCircle className=\"h-4 w-4 mr-2 flex-shrink-0\" />\n            首页推荐位置展示\n          </li>\n          <li className=\"flex items-center text-blue-800\">\n            <CheckCircle className=\"h-4 w-4 mr-2 flex-shrink-0\" />\n            专属客服支持\n          </li>\n        </ul>\n      </div>\n\n      {/* Security Notice */}\n      <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6\">\n        <div className=\"flex items-center text-gray-700\">\n          <Shield className=\"h-5 w-5 mr-2 text-green-500\" />\n          <span className=\"text-sm\">\n            您的支付信息受到银行级别的安全保护\n          </span>\n        </div>\n      </div>\n\n      {/* Stripe Payment Form */}\n      {clientSecret && (\n        <Elements\n          stripe={stripePromise}\n          options={{\n            clientSecret,\n            appearance: {\n              theme: 'stripe',\n              variables: {\n                colorPrimary: '#2563eb',\n              }\n            }\n          }}\n        >\n          <StripeCheckoutForm\n            onSuccess={handlePaymentSuccess}\n            amount={order.amount}\n          />\n        </Elements>\n      )}\n\n      <p className=\"text-gray-500 text-sm mt-4 text-center\">\n        点击支付即表示您同意我们的服务条款和隐私政策\n      </p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAUA,YAAY;AACZ,MAAM,gBAAgB,CAAA,GAAA,0JAAA,CAAA,aAAU,AAAD;AAOhB,SAAS,eAAe,EAAE,KAAK,EAAE,OAAO,EAAuB;IAC5E,MAAM,SAAS,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qCAAqC;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAQ;YACjC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,gBAAgB,KAAK,IAAI,CAAC,YAAY;YACxC,OAAO;gBACL,SAAS,KAAK,OAAO,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,OAAO,IAAI,CAAC,CAAC,uBAAuB,EAAE,MAAM,MAAM,CAAC,UAAU,CAAC;IAChE;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;kCACtB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAMvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAEzD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAGhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;kDAAe,MAAM,IAAI,EAAE,QAAQ;;;;;;;;;;;;0CAGrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;kDACb,IAAI,KAAK,MAAM,kBAAkB,EAAE,kBAAkB,CAAC;;;;;;;;;;;;0CAI3D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;kDAAuB,MAAM,GAAG;;;;;;;;;;;;0CAGlD,8OAAC;gCAAG,WAAU;;;;;;0CAEd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCAAK,WAAU;kDAAiB,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0BAMtE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;0CAGxD,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;0CAGxD,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;0CAGxD,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;;;;;;;;;;;;;0BAO5D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;YAO7B,8BACC,8OAAC,oLAAA,CAAA,WAAQ;gBACP,QAAQ;gBACR,SAAS;oBACP;oBACA,YAAY;wBACV,OAAO;wBACP,WAAW;4BACT,cAAc;wBAChB;oBACF;gBACF;0BAEA,cAAA,8OAAC,wIAAA,CAAA,UAAkB;oBACjB,WAAW;oBACX,QAAQ,MAAM,MAAM;;;;;;;;;;;0BAK1B,8OAAC;gBAAE,WAAU;0BAAyC;;;;;;;;;;;;AAK5D", "debugId": null}}]}