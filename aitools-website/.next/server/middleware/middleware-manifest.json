{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_915e9f02._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__e8693e40._.js", "server/edge/chunks/edge-wrapper_ff61254e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "IBCKtSr50mVr5qyjqc84IbmgDrtjKtpeN9PFStgL+jE=", "__NEXT_PREVIEW_MODE_ID": "b656a7d6d652671e03c19bd6040f6a52", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "497d8aa536cfcf4a29f31ef429b2101a30971fa4778ae4164e1eb1133ba4eeef", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "38d46a56280f97a8c8d305452dca1a53c83e641d973ccfbee8c44b5bedff32cf"}}}, "instrumentation": null, "functions": {}}