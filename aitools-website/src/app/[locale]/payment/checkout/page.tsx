import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import CheckoutClient from '@/app/[locale]/payment/checkout/CheckoutClient';
import { AlertCircle } from 'lucide-react';
import dbConnect from '@/lib/mongodb';
import Order from '@/models/Order';
import User from '@/models/User';
import mongoose from 'mongoose';

interface PageProps {
  searchParams: Promise<{ orderId?: string }>;
}

async function getOrderData(orderId: string, userEmail: string) {
  try {
    await dbConnect();

    if (!mongoose.Types.ObjectId.isValid(orderId)) {
      return null;
    }

    const user = await User.findOne({ email: userEmail });
    if (!user) {
      return null;
    }

    const order = await Order.findById(orderId).populate('toolId', 'name description');
    if (!order) {
      return null;
    }

    // 检查订单所有权
    if (order.userId.toString() !== user._id.toString()) {
      return null;
    }

    // 转换为普通对象以避免循环引用和序列化问题
    const orderData = {
      _id: order._id.toString(),
      type: order.type,
      amount: order.amount,
      currency: order.currency,
      status: order.status,
      description: order.description,
      selectedLaunchDate: order.selectedLaunchDate ? order.selectedLaunchDate.toISOString() : null,
      createdAt: order.createdAt ? order.createdAt.toISOString() : null,
      paidAt: order.paidAt ? order.paidAt.toISOString() : null,
      tool: order.toolId ? {
        _id: order.toolId._id.toString(),
        name: order.toolId.name,
        description: order.toolId.description
      } : null,
      toolId: order.toolId ? order.toolId._id.toString() : null
    };

    return orderData;
  } catch (error) {
    console.error('Failed to fetch order:', error);
    return null;
  }
}

export default async function CheckoutPage({ searchParams }: PageProps) {
  const session = await getServerSession(authOptions);
  const { orderId } = await searchParams;

  // 检查用户是否已登录
  if (!session?.user?.email) {
    redirect('/');
  }

  // 检查是否有订单ID
  if (!orderId) {
    return (
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">订单不存在</h1>
          <p className="text-gray-600 mb-4">未找到有效的订单ID</p>
        </div>
      </div>
    );
  }

  // 获取订单数据
  const order = await getOrderData(orderId, session.user.email);

  if (!order) {
    return (
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">订单不存在</h1>
          <p className="text-gray-600 mb-4">订单可能已被删除或不存在</p>
        </div>
      </div>
    );
  }

  // 检查订单状态
  if (order.status === 'completed') {
    redirect(`/submit/success?toolId=${order.toolId}`);
  }

  if (order.status !== 'pending') {
    return (
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">订单状态异常</h1>
          <p className="text-gray-600 mb-4">该订单无法进行支付</p>
        </div>
      </div>
    );
  }

  return <CheckoutClient order={order} orderId={orderId} />;
}

