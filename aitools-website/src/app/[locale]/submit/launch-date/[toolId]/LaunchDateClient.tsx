'use client';

import { useState } from 'react';
import { useRouter } from '@/i18n/routing';
import { useLocale } from 'next-intl';
import LaunchDateSelector from '@/components/LaunchDateSelector';

interface LaunchDateClientProps {
  toolId: string;
  locale: string;
}

export default function LaunchDateClient({ toolId, locale }: LaunchDateClientProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (option: 'free' | 'paid', selectedDate: string) => {
    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch(`/api/tools/${toolId}/launch-date`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Locale': locale,
        },
        body: JSON.stringify({
          launchOption: option,
          selectedDate,
        }),
      });

      const data = await response.json();

      if (data.success) {
        if (option === 'paid' && data.data.paymentUrl) {
          // 跳转到支付页面
          window.location.href = data.data.paymentUrl;
        } else {
          // 免费选项，直接进入审核
          router.push(`/submit/success?toolId=${toolId}`);
        }
      } else {
        setError(data.message || '提交失败');
      }
    } catch {
      setError('网络错误，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <LaunchDateSelector
      toolId={toolId}
      onSubmit={handleSubmit}
      isSubmitting={isSubmitting}
      error={error}
    />
  );
}
