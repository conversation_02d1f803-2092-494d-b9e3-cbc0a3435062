'use client';

import { useRouter } from '@/i18n/routing';
import { Clock, Home, Edit } from 'lucide-react';

interface EditLaunchDateButtonProps {
  toolId: string;
}

interface NavigationButtonsProps {}

function EditLaunchDateButton({ toolId }: EditLaunchDateButtonProps) {
  const router = useRouter();

  return (
    <button
      onClick={() => router.push(`/submit/edit-launch-date/${toolId}`)}
      className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 flex items-center"
    >
      <Edit className="h-4 w-4 mr-1" />
      修改发布日期
    </button>
  );
}

function NavigationButtons({}: NavigationButtonsProps) {
  const router = useRouter();

  return (
    <div className="flex flex-col sm:flex-row gap-4 justify-center">
      <button
        onClick={() => router.push('/profile/submitted')}
        className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 flex items-center justify-center"
      >
        <Clock className="h-4 w-4 mr-2" />
        查看我的提交
      </button>
      
      <button
        onClick={() => router.push('/')}
        className="bg-gray-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-700 flex items-center justify-center"
      >
        <Home className="h-4 w-4 mr-2" />
        返回首页
      </button>
    </div>
  );
}

const SubmitSuccessClient = {
  EditLaunchDateButton,
  NavigationButtons,
};

export default SubmitSuccessClient;
