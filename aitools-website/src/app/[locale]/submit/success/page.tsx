import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { apiClient } from '@/lib/api';
import SubmitSuccessClient from '@/app/[locale]/submit/success/SubmitSuccessClient';
import { CheckCircle, Clock, CreditCard, ArrowRight, Home, Calendar, Edit } from 'lucide-react';

interface PageProps {
  searchParams: Promise<{ toolId?: string; paid?: string }>;
}

async function getToolData(toolId: string) {
  try {
    const response = await apiClient.getUserTool(toolId);

    if (!response.success || !response.data) {
      return null;
    }

    return {
      _id: response.data._id,
      name: response.data.name,
      status: response.data.status,
      launchOption: response.data.launchOption,
      selectedLaunchDate: response.data.selectedLaunchDate,
    };
  } catch (error) {
    console.error('Failed to fetch tool:', error);
    return null;
  }
}

export default async function SubmitSuccessPage({ searchParams }: PageProps) {
  const session = await getServerSession(authOptions);
  const { toolId, paid } = await searchParams;

  // 检查用户是否已登录
  if (!session?.user?.email) {
    redirect('/');
  }

  // 检查是否有工具ID
  if (!toolId) {
    redirect('/');
  }

  const isPaid = paid === 'true';

  // 获取工具数据
  const tool = await getToolData(toolId);

  if (!tool) {
    redirect('/');
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Success Header */}
      <div className="text-center mb-8">
        <div className="flex justify-center mb-4">
          <div className="bg-green-100 rounded-full p-3">
            <CheckCircle className="h-12 w-12 text-green-600" />
          </div>
        </div>

        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {isPaid ? '支付成功！' : '提交成功！'}
        </h1>

        <p className="text-lg text-gray-600">
          {isPaid
            ? '您的工具已进入优先审核队列，我们会在1个工作日内完成审核。'
            : '您的工具已进入审核队列，我们会在1-3个工作日内完成审核。'
          }
        </p>
      </div>

      {/* Tool Info */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">工具信息</h2>

        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">{tool.name}</h3>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>提交状态：</span>
                <span className="font-medium text-green-600">已提交</span>
              </div>
              <div className="flex justify-between">
                <span>审核状态：</span>
                <span className="font-medium text-yellow-600">
                  {tool.status === 'pending' ? '等待审核' : '草稿'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>发布选项：</span>
                <span className="font-medium">
                  {tool.launchOption === 'paid' ? '优先发布' : '免费发布'}
                </span>
              </div>
              {tool.selectedLaunchDate && (
                <div className="flex justify-between">
                  <span>计划发布日期：</span>
                  <span className="font-medium">
                    {new Date(tool.selectedLaunchDate).toLocaleDateString('zh-CN')}
                  </span>
                </div>
              )}
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">接下来会发生什么？</h4>
            <div className="space-y-3">
              <div className="flex items-start">
                <div className="bg-blue-100 rounded-full p-1 mr-3 mt-0.5">
                  <Clock className="h-3 w-3 text-blue-600" />
                </div>
                <div className="text-sm text-gray-600">
                  <div className="font-medium">审核阶段</div>
                  <div>我们的团队会审核您的工具信息</div>
                </div>
              </div>

              <div className="flex items-start">
                <div className="bg-green-100 rounded-full p-1 mr-3 mt-0.5">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                </div>
                <div className="text-sm text-gray-600">
                  <div className="font-medium">审核通过</div>
                  <div>工具将在指定日期发布到平台</div>
                </div>
              </div>

              <div className="flex items-start">
                <div className="bg-purple-100 rounded-full p-1 mr-3 mt-0.5">
                  <ArrowRight className="h-3 w-3 text-purple-600" />
                </div>
                <div className="text-sm text-gray-600">
                  <div className="font-medium">正式发布</div>
                  <div>用户可以在平台上发现您的工具</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Launch Date Management - 所有用户都可以看到 */}
      <div className={`${tool.launchOption === 'paid' ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'} border rounded-lg p-6 mb-8`}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            {tool.launchOption === 'paid' ? (
              <>
                <CreditCard className="h-6 w-6 text-blue-600 mr-2" />
                <h3 className="text-lg font-semibold text-blue-900">
                  优先发布服务已激活
                </h3>
              </>
            ) : (
              <>
                <Calendar className="h-6 w-6 text-gray-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">
                  发布日期管理
                </h3>
              </>
            )}
          </div>

          {/* 修改发布日期按钮 - 使用客户端组件处理导航 */}
          {['pending', 'approved'].includes(tool.status) && (
            <SubmitSuccessClient.EditLaunchDateButton toolId={toolId} />
          )}
        </div>

        {/* 服务特性 - 只有付费用户显示 */}
        {tool.launchOption === 'paid' && (
          <div className="grid md:grid-cols-2 gap-4 mb-4">
            <div className="space-y-2">
              <div className="flex items-center text-blue-800">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">优先审核（1个工作日内）</span>
              </div>
              <div className="flex items-center text-blue-800">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">首页推荐位置展示</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center text-blue-800">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">自定义发布日期</span>
              </div>
              <div className="flex items-center text-blue-800">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">专属客服支持</span>
              </div>
            </div>
          </div>
        )}

        {/* 免费用户的服务说明 */}
        {tool.launchOption === 'free' && (
          <div className="bg-gray-100 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">免费发布服务</h4>
            <div className="space-y-2">
              <div className="flex items-center text-gray-700">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">标准审核流程（1-3个工作日）</span>
              </div>
              <div className="flex items-center text-gray-700">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">可修改发布日期（一个月后起）</span>
              </div>
              <div className="flex items-center text-gray-700">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">标准展示位置</span>
              </div>
            </div>
          </div>
        )}

        {/* 当前发布日期显示 */}
        {tool.selectedLaunchDate && (
          <div className={`bg-white rounded-lg p-4 border ${tool.launchOption === 'paid' ? 'border-blue-200' : 'border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Calendar className={`h-5 w-5 mr-2 ${tool.launchOption === 'paid' ? 'text-blue-600' : 'text-gray-600'}`} />
                <span className="text-sm font-medium text-gray-900">当前发布日期：</span>
              </div>
              <span className={`text-sm font-semibold ${tool.launchOption === 'paid' ? 'text-blue-600' : 'text-gray-600'}`}>
                {new Date(tool.selectedLaunchDate).toLocaleDateString('zh-CN')}
              </span>
            </div>
            {['pending', 'approved'].includes(tool.status) && (
              <p className="text-xs text-gray-600 mt-2">
                💡 您可以在工具发布前随时修改发布日期
                {tool.launchOption === 'free' && ' (限制：一个月后的日期)'}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Contact Info */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          需要帮助？
        </h3>
        <p className="text-gray-600 mb-4">
          如果您有任何问题或需要修改工具信息，请联系我们：
        </p>
        <div className="space-y-2 text-sm text-gray-600">
          <div>📧 邮箱：<EMAIL></div>
        </div>
      </div>

      {/* Action Buttons - 使用客户端组件处理导航 */}
      <SubmitSuccessClient.NavigationButtons />
    </div>
  );
}
