import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import User from '@/models/User';
import mongoose from 'mongoose';
import { getApiMessage } from '@/lib/api-messages';
import { Locale } from '@/i18n/config';

interface RouteParams {
  params: Promise<{ id: string; locale?: string }>;
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;
    const session = await getServerSession(authOptions);
    
    // Extract locale from URL path
    const pathname = request.nextUrl.pathname;
    const locale = (pathname.startsWith('/en') ? 'en' : 'zh') as Locale;

    // 检查用户是否已登录
    if (!session?.user?.email) {
      return NextResponse.json(
        { 
          success: false, 
          message: getApiMessage(locale, 'auth.login_required') 
        },
        { status: 401 }
      );
    }

    await dbConnect();

    // 验证工具ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { 
          success: false, 
          message: getApiMessage(locale, 'tool.invalid_id') 
        },
        { status: 400 }
      );
    }

    // 查找用户
    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          message: getApiMessage(locale, 'user.not_found') 
        },
        { status: 404 }
      );
    }

    // 查找工具
    const tool = await Tool.findById(id);
    if (!tool) {
      return NextResponse.json(
        { 
          success: false, 
          message: getApiMessage(locale, 'tool.not_found') 
        },
        { status: 404 }
      );
    }

    // 检查工具所有权
    if (!tool.userId || tool.userId.toString() !== user._id.toString()) {
      return NextResponse.json(
        { 
          success: false, 
          message: getApiMessage(locale, 'tool.access_denied') 
        },
        { status: 403 }
      );
    }

    // 返回工具数据（只返回必要的字段）
    const toolData = {
      _id: tool._id.toString(),
      name: tool.name,
      tagline: tool.tagline,
      description: tool.description,
      website: tool.website,
      logo: tool.logo,
      category: tool.category,
      tags: tool.tags,
      pricing: tool.pricing,
      status: tool.status,
      launchOption: tool.launchOption,
      selectedLaunchDate: tool.selectedLaunchDate ? tool.selectedLaunchDate.toISOString() : null,
      paymentStatus: tool.paymentStatus,
      paidAt: tool.paidAt ? tool.paidAt.toISOString() : null,
      submittedAt: tool.submittedAt ? tool.submittedAt.toISOString() : null,
      reviewNotes: tool.reviewNotes,
      reviewedAt: tool.reviewedAt ? tool.reviewedAt.toISOString() : null,
      createdAt: tool.createdAt ? tool.createdAt.toISOString() : null,
      updatedAt: tool.updatedAt ? tool.updatedAt.toISOString() : null,
    };

    return NextResponse.json({
      success: true,
      data: toolData
    });

  } catch (error) {
    console.error('Error fetching user tool:', error);
    
    // Extract locale from URL path for error response
    const pathname = request.nextUrl.pathname;
    const locale = (pathname.startsWith('/en') ? 'en' : 'zh') as Locale;
    
    return NextResponse.json(
      { 
        success: false, 
        message: getApiMessage(locale, 'common.server_error') 
      },
      { status: 500 }
    );
  }
}
