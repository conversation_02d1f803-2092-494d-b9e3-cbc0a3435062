'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useRouter } from '@/i18n/routing';
import { useSession } from 'next-auth/react';
import { useTranslations, useLocale } from 'next-intl';
import Layout from '@/components/Layout';
import LaunchDateSelector from '@/components/LaunchDateSelector';
import { AlertCircle, RefreshCw } from 'lucide-react';

export default function ReapplyLaunchDatePage() {
  const params = useParams();
  const router = useRouter();
  const locale = useLocale();
  const { status } = useSession();
  const t = useTranslations('submit');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [tool, setTool] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const toolId = params.toolId as string;

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/');
      return;
    }

    if (status === 'authenticated' && toolId) {
      fetchToolInfo();
    }
  }, [status, toolId]);

  const fetchToolInfo = async () => {
    try {
      const response = await fetch(`/api/tools/${toolId}`);
      const data = await response.json();
      
      if (data.success) {
        setTool(data.data);
        // 检查工具状态 - 只有draft状态的工具可以重新申请
        if (data.data.status !== 'draft') {
          setError(t('errors.tool_not_draft'));
          return;
        }
      } else {
        setError(t('errors.tool_not_found'));
      }
    } catch {
      setError(t('errors.fetch_failed'));
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (option: 'free' | 'paid', selectedDate: string) => {
    setIsSubmitting(true);
    setError('');

    try {
      const response = await fetch(`/api/tools/${toolId}/launch-date`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Locale': locale,
        },
        body: JSON.stringify({
          launchOption: option,
          selectedDate,
        }),
      });

      const data = await response.json();

      if (data.success) {
        if (option === 'paid' && data.data.paymentUrl) {
          // 跳转到支付页面
          window.location.href = data.data.paymentUrl;
        } else {
          // 免费选项，直接进入审核
          router.push(`/submit/success?toolId=${toolId}&reapply=true`);
        }
      } else {
        setError(data.message || t('errors.submit_failed'));
      }
    } catch {
      setError(t('errors.network_error'));
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">{t('loading')}</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">{t('errors.title')}</h1>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => router.push(`/profile/submitted`)}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
            >
              {t('actions.back_to_submitted')}
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <RefreshCw className="h-12 w-12 text-blue-500 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {t('reapply.title')}
          </h1>
          <p className="text-lg text-gray-600">
            {t('reapply.subtitle')}
          </p>
        </div>

        {/* Tool Info */}
        {tool && (
          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              {tool.name}
            </h2>
            <p className="text-gray-600">{tool.description}</p>
            
            {/* 显示之前的付费状态信息 */}
            {tool.paymentRequired && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>{t('reapply.previous_payment_info')}</strong>
                  {tool.paymentStatus === 'completed' 
                    ? t('reapply.payment_completed') 
                    : t('reapply.payment_pending')
                  }
                </p>
              </div>
            )}
          </div>
        )}

        {/* Launch Date Selector */}
        {tool && (
          <LaunchDateSelector
            toolId={toolId}
            currentOption={tool.launchOption || 'free'}
            currentDate={tool.selectedLaunchDate}
            isEditing={true}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
            error={error}
          />
        )}
      </div>
    </Layout>
  );
}
